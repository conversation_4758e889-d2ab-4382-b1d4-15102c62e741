import torch
import numpy as np
from numpy.linalg import norm


class Memory(object):
    """
        Create the empty memory buffer for variable-length control points
    """

    def __init__(self, size, dimension=384):
        self.memory = {}
        self.size = size
        self.dimension = dimension

    def reset(self):
        self.memory = {}

    def get_size(self):
        return len(self.memory)

    def push(self, keys, control_points):
        """存储变长控制点数据
        Args:
            keys: [B, dimension] 图像摘要
            control_points: [B, N, 7] 控制点参数，N可变
        """
        for i, key in enumerate(keys):
            if len(self.memory.keys()) > self.size:
                self.memory.pop(list(self.memory)[0])

            # 存储控制点数据和数量信息
            control_data = {
                'points': control_points[i],  # [N, 7]
                'num_points': control_points[i].shape[0]
            }

            self.memory.update(
                {key.reshape(self.dimension).tobytes(): control_data})

    def _prepare_batch(self, sample_data_list, attention_weight, target_num_points):
        """准备批次数据，处理变长控制点
        Args:
            sample_data_list: 包含控制点数据的列表
            attention_weight: 注意力权重
            target_num_points: 目标控制点数量
        Returns:
            interpolated_points: [target_num_points, 7] 插值后的控制点
        """
        attention_weight = np.array(attention_weight / 0.2)
        attention_weight = np.exp(attention_weight) / (np.sum(np.exp(attention_weight)))

        # 收集所有控制点和权重
        all_points = []
        all_weights = []

        for i, data in enumerate(sample_data_list):
            points = data['points']  # [Ni, 7]
            weight = attention_weight[i]

            # 为每个点分配权重
            point_weights = np.full(len(points), weight / len(points))
            all_points.append(points)
            all_weights.append(point_weights)

        # 合并所有点
        if all_points:
            combined_points = np.vstack(all_points)
            combined_weights = np.concatenate(all_weights)

            # 如果点数超过目标数量，进行加权采样
            if len(combined_points) > target_num_points:
                # 归一化权重
                combined_weights = combined_weights / np.sum(combined_weights)
                # 加权采样
                indices = np.random.choice(len(combined_points), target_num_points,
                                         replace=False, p=combined_weights)
                result_points = combined_points[indices]
            elif len(combined_points) < target_num_points:
                # 如果点数不足，重复采样
                indices = np.random.choice(len(combined_points), target_num_points, replace=True)
                result_points = combined_points[indices]
            else:
                result_points = combined_points
        else:
            # 如果没有数据，返回随机初始化
            result_points = np.random.randn(target_num_points, 7) * 0.1

        return torch.FloatTensor(result_points)

    def get_neighbours(self, keys, k, target_num_points):
        """
        Returns samples from buffer using nearest neighbour approach
        Args:
            keys: [B, dimension] 查询键
            k: 邻居数量
            target_num_points: 目标控制点数量
        Returns:
            samples: [B, target_num_points, 7] 插值后的控制点
            avg_score: 平均相似度分数
        """
        if len(self.memory.keys()) == 0:
            # 如果内存为空，返回随机初始化
            samples = torch.randn(len(keys), target_num_points, 7) * 0.1
            return samples, 0.0

        samples = []

        keys = keys.reshape(len(keys), self.dimension)
        total_keys = len(self.memory.keys())
        self.all_keys = np.frombuffer(
            np.asarray(list(self.memory.keys())), dtype=np.float32).reshape(total_keys, self.dimension)

        for key in keys:
            similarity_scores = np.dot(self.all_keys, key.T) / (norm(self.all_keys, axis=1) * norm(key.T))

            K_neighbour_keys = self.all_keys[np.argpartition(similarity_scores, -k)[-k:]]
            neighbours = [self.memory[nkey.tobytes()] for nkey in K_neighbour_keys]

            attention_weight = np.dot(K_neighbour_keys, key.T) / (norm(K_neighbour_keys, axis=1) * norm(key.T))
            batch = self._prepare_batch(neighbours, attention_weight, target_num_points)
            samples.append(batch)

        return torch.stack(samples), np.mean(similarity_scores)
