import numpy as np
import cv2


def poisson_disk_sampling(mask, num_points, min_distance=0.02):
    """在掩码区域内进行泊松盘采样
    Args:
        mask: [H, W] 二值掩码，1表示有效区域
        num_points: 目标采样点数
        min_distance: 最小距离（归一化坐标）
    Returns:
        points: [N, 2] 采样点坐标（归一化到[0,1]）
    """
    H, W = mask.shape
    
    # 如果掩码为空，返回中心点
    if np.sum(mask) == 0:
        return np.array([[0.5, 0.5]])
    
    # 获取掩码内的所有像素坐标
    valid_coords = np.where(mask > 0)
    if len(valid_coords[0]) == 0:
        return np.array([[0.5, 0.5]])
    
    # 转换为归一化坐标
    valid_points = np.column_stack([
        valid_coords[1] / W,  # x坐标
        valid_coords[0] / H   # y坐标
    ])
    
    # 如果有效点数少于目标点数，直接返回所有有效点
    if len(valid_points) <= num_points:
        return valid_points
    
    # 泊松盘采样
    sampled_points = []
    remaining_points = valid_points.copy()
    
    # 随机选择第一个点
    first_idx = np.random.randint(len(remaining_points))
    sampled_points.append(remaining_points[first_idx])
    remaining_points = np.delete(remaining_points, first_idx, axis=0)
    
    # 迭代采样
    while len(sampled_points) < num_points and len(remaining_points) > 0:
        # 计算剩余点到已采样点的最小距离
        distances_to_sampled = []
        for point in remaining_points:
            min_dist = float('inf')
            for sampled_point in sampled_points:
                dist = np.linalg.norm(point - sampled_point)
                min_dist = min(min_dist, dist)
            distances_to_sampled.append(min_dist)
        
        distances_to_sampled = np.array(distances_to_sampled)
        
        # 找到满足最小距离约束的点
        valid_indices = np.where(distances_to_sampled >= min_distance)[0]
        
        if len(valid_indices) == 0:
            # 如果没有满足约束的点，选择距离最大的点
            next_idx = np.argmax(distances_to_sampled)
        else:
            # 在满足约束的点中随机选择
            next_idx = np.random.choice(valid_indices)
        
        sampled_points.append(remaining_points[next_idx])
        remaining_points = np.delete(remaining_points, next_idx, axis=0)
    
    # 如果采样点数不足，用随机点补充
    while len(sampled_points) < num_points:
        if len(remaining_points) > 0:
            idx = np.random.randint(len(remaining_points))
            sampled_points.append(remaining_points[idx])
            remaining_points = np.delete(remaining_points, idx, axis=0)
        else:
            # 在掩码区域内随机生成点
            random_point = generate_random_point_in_mask(mask)
            sampled_points.append(random_point)
    
    return np.array(sampled_points)


def generate_random_point_in_mask(mask):
    """在掩码区域内生成随机点
    Args:
        mask: [H, W] 二值掩码
    Returns:
        point: [2] 随机点坐标（归一化）
    """
    H, W = mask.shape
    valid_coords = np.where(mask > 0)
    
    if len(valid_coords[0]) == 0:
        return np.array([0.5, 0.5])
    
    idx = np.random.randint(len(valid_coords[0]))
    y, x = valid_coords[0][idx], valid_coords[1][idx]
    
    return np.array([x / W, y / H])


def adaptive_poisson_sampling(mask, target_density, min_distance=0.02):
    """基于密度的自适应泊松盘采样
    Args:
        mask: [H, W] 二值掩码
        target_density: 目标密度（点数/面积）
        min_distance: 最小距离
    Returns:
        points: [N, 2] 采样点坐标
    """
    H, W = mask.shape
    mask_area = np.sum(mask) / (H * W)  # 归一化面积
    
    if mask_area == 0:
        return np.array([[0.5, 0.5]])
    
    # 根据面积和密度计算目标点数
    num_points = max(1, int(mask_area * target_density))
    
    return poisson_disk_sampling(mask, num_points, min_distance)


def create_disc_mask_from_geometry(geometry_info, image_shape):
    """根据几何信息创建视盘掩码
    Args:
        geometry_info: 几何信息字典
        image_shape: (H, W) 图像尺寸
    Returns:
        mask: [H, W] 视盘掩码
    """
    H, W = image_shape
    mask = np.zeros((H, W), dtype=np.uint8)
    
    if 'disc_contour' in geometry_info and len(geometry_info['disc_contour']) > 2:
        # 使用轮廓创建掩码
        contour = geometry_info['disc_contour'].copy()
        contour[:, 0] *= W
        contour[:, 1] *= H
        contour = contour.astype(np.int32)
        cv2.fillPoly(mask, [contour], 1)
    else:
        # 使用质心创建圆形掩码
        cx, cy = geometry_info['disc_centroid']
        center = (int(cx * W), int(cy * H))
        radius = int(np.sqrt(geometry_info.get('disc_area', 0.01)) * min(H, W) / 2)
        radius = max(radius, 10)  # 最小半径
        cv2.circle(mask, center, radius, 1, -1)
    
    return mask
