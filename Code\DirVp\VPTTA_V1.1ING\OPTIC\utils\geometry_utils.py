import torch
import numpy as np
import cv2
from scipy.spatial.distance import pdist, squareform


def extract_geometry_info(prob_map, threshold=0.5):
    """从概率图中提取几何信息
    Args:
        prob_map: [H, W, 2] 概率图，通道0为视盘，通道1为视杯
        threshold: 二值化阈值
    Returns:
        geometry_info: 包含质心、面积、轮廓等信息的字典
    """
    H, W = prob_map.shape[:2]
    
    # 二值化
    disc_mask = (prob_map[:, :, 0] > threshold).astype(np.uint8)
    cup_mask = (prob_map[:, :, 1] > threshold).astype(np.uint8)
    
    # 形态学清洗
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    disc_mask = cv2.morphologyEx(disc_mask, cv2.MORPH_OPEN, kernel)
    cup_mask = cv2.morphologyEx(cup_mask, cv2.MORPH_OPEN, kernel)
    
    geometry_info = {}
    
    # 处理视盘
    if np.sum(disc_mask) > 0:
        # 计算质心
        moments = cv2.moments(disc_mask)
        if moments['m00'] != 0:
            cx = moments['m10'] / moments['m00'] / W  # 归一化到[0,1]
            cy = moments['m01'] / moments['m00'] / H
            geometry_info['disc_centroid'] = (cx, cy)
        else:
            geometry_info['disc_centroid'] = (0.5, 0.5)
        
        # 计算面积（归一化）
        geometry_info['disc_area'] = np.sum(disc_mask) / (H * W)
        
        # 提取轮廓
        contours, _ = cv2.findContours(disc_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            # 选择最大轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            # 归一化轮廓坐标到[0,1]
            normalized_contour = largest_contour.reshape(-1, 2).astype(np.float32)
            normalized_contour[:, 0] /= W
            normalized_contour[:, 1] /= H
            geometry_info['disc_contour'] = normalized_contour
            geometry_info['disc_perimeter'] = cv2.arcLength(largest_contour, True) / (H + W)
        else:
            geometry_info['disc_contour'] = np.array([[0.5, 0.5]])
            geometry_info['disc_perimeter'] = 0.0
    else:
        geometry_info['disc_centroid'] = (0.5, 0.5)
        geometry_info['disc_area'] = 0.0
        geometry_info['disc_contour'] = np.array([[0.5, 0.5]])
        geometry_info['disc_perimeter'] = 0.0
    
    # 处理视杯
    if np.sum(cup_mask) > 0:
        # 计算质心
        moments = cv2.moments(cup_mask)
        if moments['m00'] != 0:
            cx = moments['m10'] / moments['m00'] / W
            cy = moments['m01'] / moments['m00'] / H
            geometry_info['cup_centroid'] = (cx, cy)
        else:
            geometry_info['cup_centroid'] = geometry_info['disc_centroid']
        
        # 计算面积（归一化）
        geometry_info['cup_area'] = np.sum(cup_mask) / (H * W)
        
        # 提取轮廓
        contours, _ = cv2.findContours(cup_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            normalized_contour = largest_contour.reshape(-1, 2).astype(np.float32)
            normalized_contour[:, 0] /= W
            normalized_contour[:, 1] /= H
            geometry_info['cup_contour'] = normalized_contour
            geometry_info['cup_perimeter'] = cv2.arcLength(largest_contour, True) / (H + W)
        else:
            geometry_info['cup_contour'] = np.array([geometry_info['cup_centroid']])
            geometry_info['cup_perimeter'] = 0.0
    else:
        geometry_info['cup_centroid'] = geometry_info['disc_centroid']
        geometry_info['cup_area'] = 0.0
        geometry_info['cup_contour'] = np.array([geometry_info['disc_centroid']])
        geometry_info['cup_perimeter'] = 0.0
    
    return geometry_info


def sample_contour_points(contour, num_points):
    """沿轮廓等间距采样点
    Args:
        contour: [N, 2] 轮廓点坐标
        num_points: 采样点数量
    Returns:
        sampled_points: [num_points, 2] 采样的点坐标
    """
    if len(contour) <= num_points:
        # 如果轮廓点数不足，直接返回所有点并补充
        sampled_points = contour.copy()
        while len(sampled_points) < num_points:
            sampled_points = np.vstack([sampled_points, contour])
        return sampled_points[:num_points]
    
    # 计算累积弧长
    distances = np.sqrt(np.sum(np.diff(contour, axis=0)**2, axis=1))
    cumulative_distances = np.concatenate([[0], np.cumsum(distances)])
    total_length = cumulative_distances[-1]
    
    # 等间距采样
    sample_distances = np.linspace(0, total_length, num_points, endpoint=False)
    sampled_points = []
    
    for target_dist in sample_distances:
        # 找到最接近的点
        idx = np.searchsorted(cumulative_distances, target_dist)
        if idx == 0:
            sampled_points.append(contour[0])
        elif idx >= len(contour):
            sampled_points.append(contour[-1])
        else:
            # 线性插值
            t = (target_dist - cumulative_distances[idx-1]) / (cumulative_distances[idx] - cumulative_distances[idx-1])
            point = contour[idx-1] + t * (contour[idx] - contour[idx-1])
            sampled_points.append(point)
    
    return np.array(sampled_points)


def generate_base_grid(grid_size):
    """生成基础网格控制点
    Args:
        grid_size: 网格大小（如5表示5x5网格）
    Returns:
        grid_coords: [grid_size*grid_size, 2] 网格坐标
    """
    x = np.linspace(0, 1, grid_size)
    y = np.linspace(0, 1, grid_size)
    grid_y, grid_x = np.meshgrid(y, x)
    grid_coords = np.stack([grid_x.flatten(), grid_y.flatten()], axis=1)
    return grid_coords


def constrain_point_count(target_count, min_count, max_count):
    """约束控制点数量在合理范围内
    Args:
        target_count: 目标点数
        min_count: 最小点数
        max_count: 最大点数
    Returns:
        constrained_count: 约束后的点数
    """
    return max(min_count, min(target_count, max_count))
