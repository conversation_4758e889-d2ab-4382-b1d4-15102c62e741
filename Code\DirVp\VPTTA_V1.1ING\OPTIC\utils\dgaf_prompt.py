import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from .geometry_utils import extract_geometry_info, sample_contour_points, constrain_point_count, create_disc_mask_from_geometry
from .poisson_sampling import poisson_disk_sampling


def generate_base_grid(grid_size):
    """生成基础网格点
    Args:
        grid_size: 网格大小 (例如5表示5x5网格)
    Returns:
        grid_points: [grid_size*grid_size, 2] 网格点坐标
    """
    x = np.linspace(0, 1, grid_size)
    y = np.linspace(0, 1, grid_size)
    grid_y, grid_x = np.meshgrid(y, x)
    grid_points = np.stack([grid_x.flatten(), grid_y.flatten()], axis=1)
    return grid_points
from .geometry_utils import generate_base_grid, sample_contour_points, constrain_point_count
from .poisson_sampling import poisson_disk_sampling, create_disc_mask_from_geometry


class DGAFPrompt(nn.Module):
    def __init__(self, base_grid_size=5, min_total_points=50, max_total_points=150,
                 interior_density_factor=0.8, boundary_density_factor=1.2,
                 image_size=512, device='cuda:0'):
        super().__init__()
        self.base_grid_size = base_grid_size
        self.min_total_points = min_total_points
        self.max_total_points = max_total_points
        self.interior_density_factor = interior_density_factor
        self.boundary_density_factor = boundary_density_factor
        self.image_size = image_size
        self.device = device

        # 软阈值陡峭度参数
        self.sigmoid_steepness = 10.0

        # 初始化为空，将在第一次调用时动态分配
        self.num_control_points = 0
        self.delta_p = None
        self.delta_a = None
        self.radius = None
        self.alpha = None
        self.control_coords = None

    def _generate_dynamic_control_points(self, geometry_info):
        """根据几何信息生成动态控制点
        Args:
            geometry_info: 包含视盘/视杯几何信息的字典
        Returns:
            control_coords: [N, 2] 控制点坐标
        """
        all_points = []

        # 1. 生成基础网格点
        base_points = generate_base_grid(self.base_grid_size)
        all_points.append(base_points)

        # 2. 生成内部点
        disc_area = geometry_info.get('disc_area', 0.01)
        num_interior = int(disc_area * self.interior_density_factor * 1000)  # 缩放因子
        num_interior = constrain_point_count(num_interior, 10, 80)

        # 创建视盘掩码用于泊松采样
        disc_mask = create_disc_mask_from_geometry(geometry_info, (self.image_size, self.image_size))
        interior_points = poisson_disk_sampling(disc_mask, num_interior, min_distance=0.02)
        all_points.append(interior_points)

        # 3. 生成边界点
        # 视盘边界点
        disc_contour = geometry_info.get('disc_contour', np.array([[0.5, 0.5]]))
        disc_perimeter = geometry_info.get('disc_perimeter', 0.1)
        num_disc_boundary = int(disc_perimeter * self.boundary_density_factor * 200)
        num_disc_boundary = constrain_point_count(num_disc_boundary, 8, 40)

        if len(disc_contour) > 1:
            disc_boundary_points = sample_contour_points(disc_contour, num_disc_boundary)
            all_points.append(disc_boundary_points)

        # 视杯边界点
        cup_contour = geometry_info.get('cup_contour', np.array([[0.5, 0.5]]))
        cup_perimeter = geometry_info.get('cup_perimeter', 0.05)
        num_cup_boundary = int(cup_perimeter * self.boundary_density_factor * 300)
        num_cup_boundary = constrain_point_count(num_cup_boundary, 5, 30)

        if len(cup_contour) > 1:
            cup_boundary_points = sample_contour_points(cup_contour, num_cup_boundary)
            all_points.append(cup_boundary_points)

        # 合并所有控制点
        all_control_points = np.vstack(all_points)

        # 约束总点数
        total_points = len(all_control_points)
        if total_points > self.max_total_points:
            # 随机采样到最大点数
            indices = np.random.choice(total_points, self.max_total_points, replace=False)
            all_control_points = all_control_points[indices]
        elif total_points < self.min_total_points:
            # 补充随机点到最小点数
            needed = self.min_total_points - total_points
            random_points = np.random.rand(needed, 2)
            all_control_points = np.vstack([all_control_points, random_points])

        return torch.from_numpy(all_control_points).float().to(self.device)

    def _allocate_parameters(self, num_points):
        """动态分配控制点参数
        Args:
            num_points: 控制点数量
        """
        self.num_control_points = num_points

        # 位移参数 Δp: [num_points, 2]
        self.delta_p = nn.Parameter(torch.randn(num_points, 2, device=self.device) * 0.05)

        # 外观调整参数 Δa: [num_points, 3]
        self.delta_a = nn.Parameter(torch.randn(num_points, 3, device=self.device) * 0.01)

        # 影响半径参数 r: [num_points]
        self.radius = nn.Parameter(torch.empty(num_points, device=self.device).uniform_(0.3, 0.8))

        # 显著性权重参数 α: [num_points]
        self.alpha = nn.Parameter(torch.empty(num_points, device=self.device).uniform_(0.8, 1.2))

    def initialize_from_geometry(self, geometry_info):
        """根据几何信息初始化控制点
        Args:
            geometry_info: 几何信息字典
        """
        # 生成动态控制点坐标
        self.control_coords = self._generate_dynamic_control_points(geometry_info)

        # 分配参数
        self._allocate_parameters(len(self.control_coords))

    def update(self, init_control_points, target_coords=None):
        """使用Memory Bank检索到的控制点参数进行初始化
        Args:
            init_control_points: [1, old_num_points, 7] 或 [old_num_points, 7] 历史控制点参数
            target_coords: [new_num_points, 2] 目标控制点坐标
        """
        with torch.no_grad():
            if init_control_points.dim() == 3:
                init_control_points = init_control_points.squeeze(0)

            init_control_points = init_control_points.to(self.device)
            old_coords = init_control_points[:, :2]  # 历史坐标（位移参数）
            old_params = init_control_points[:, 2:]  # 历史参数

            if target_coords is None:
                target_coords = self.control_coords

            # 使用最近邻插值将历史参数映射到新的控制点
            if len(old_coords) > 0 and len(target_coords) > 0:
                # 计算距离矩阵
                distances = torch.cdist(target_coords, old_coords)
                nearest_indices = torch.argmin(distances, dim=1)

                # 插值参数
                interpolated_params = old_params[nearest_indices]

                # 更新参数
                self.delta_a.copy_(interpolated_params[:, :3])  # 外观参数
                self.radius.copy_(interpolated_params[:, 3])    # 影响半径参数
                self.alpha.copy_(interpolated_params[:, 4])     # 显著性权重参数

                # 位移参数使用小的随机值
                self.delta_p.copy_(torch.randn_like(self.delta_p) * 0.02)

    def _idw_interpolation(self, coords, control_coords, control_values, influence_radii, alpha_weights):
        """反距离加权插值
        Args:
            coords: [H*W, 2] 目标像素坐标
            control_coords: [num_points, 2] 控制点坐标
            control_values: [num_points, channels] 控制点值
            influence_radii: [num_points] 影响半径
            alpha_weights: [num_points] 显著性权重
        Returns:
            interpolated_values: [H*W, channels] 插值结果
        """
        if len(control_coords) == 0:
            # 如果没有控制点，返回零值
            return torch.zeros(len(coords), control_values.shape[1] if len(control_values.shape) > 1 else 1,
                             device=coords.device, dtype=coords.dtype)

        # 计算距离矩阵 [H*W, num_points]
        distances = torch.cdist(coords, control_coords)

        # 应用影响半径约束（使用可微分的软阈值）
        radius_mask = torch.sigmoid(self.sigmoid_steepness * (influence_radii.unsqueeze(0) - distances))

        # 计算IDW权重，避免除零
        epsilon = 1e-8
        weights = 1.0 / (distances + epsilon)
        weights = weights * radius_mask

        # 应用显著性权重
        weights = weights * alpha_weights.unsqueeze(0)

        # 归一化权重
        weight_sum = weights.sum(dim=1, keepdim=True)
        weights = weights / (weight_sum + epsilon)

        # 执行插值
        interpolated_values = torch.matmul(weights, control_values)

        return interpolated_values

    def _generate_dense_field(self, image_shape):
        """生成稠密变换场
        Args:
            image_shape: (H, W)
        Returns:
            displacement_field: [H, W, 2] 位移场
            appearance_field: [H, W, 3] 外观场
        """
        if self.control_coords is None or self.delta_p is None:
            # 如果控制点未初始化，返回零场
            H, W = image_shape
            displacement_field = torch.zeros(H, W, 2, device=self.device)
            appearance_field = torch.zeros(H, W, 3, device=self.device)
            return displacement_field, appearance_field

        H, W = image_shape

        # 生成像素坐标网格 [H*W, 2]
        y_coords = torch.linspace(0, 1, H, device=self.device)
        x_coords = torch.linspace(0, 1, W, device=self.device)
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords)
        pixel_coords = torch.stack([grid_x.flatten(), grid_y.flatten()], dim=1)

        # 插值生成位移场
        displacement_flat = self._idw_interpolation(
            pixel_coords, self.control_coords, self.delta_p, self.radius, self.alpha
        )
        displacement_field = displacement_flat.view(H, W, 2)

        # 插值生成外观场
        appearance_flat = self._idw_interpolation(
            pixel_coords, self.control_coords, self.delta_a, self.radius, self.alpha
        )
        appearance_field = appearance_flat.view(H, W, 3)

        return displacement_field, appearance_field

    def forward(self, x):
        """前向传播
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            transformed_image: [B, 3, H, W] 变换后图像
            image_summary: [B, 384] 图像摘要
        """
        B, C, H, W = x.shape

        # 生成稠密变换场
        displacement_field, appearance_field = self._generate_dense_field((H, W))

        # 应用几何变换
        transformed_image = self._apply_geometric_transform(x, displacement_field)

        # 应用外观变换
        transformed_image = self._apply_appearance_transform(transformed_image, appearance_field)

        # 生成图像摘要
        image_summary = self._generate_image_summary(x)

        return transformed_image, image_summary

    def _apply_geometric_transform(self, x, displacement_field):
        """应用几何变换
        Args:
            x: [B, 3, H, W] 输入图像
            displacement_field: [H, W, 2] 位移场
        Returns:
            transformed_x: [B, 3, H, W] 几何变换后图像
        """
        B, C, H, W = x.shape

        # 生成采样网格
        # 创建标准化坐标网格 [-1, 1]
        y_coords = torch.linspace(-1, 1, H, device=self.device)
        x_coords = torch.linspace(-1, 1, W, device=self.device)
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords)
        base_grid = torch.stack([grid_x, grid_y], dim=2)  # [H, W, 2]

        # 将位移场从[0,1]坐标转换为[-1,1]坐标系的位移
        # displacement_field是在[0,1]坐标系中的位移，需要转换为像素位移
        pixel_displacement = displacement_field * torch.tensor([W-1, H-1], device=self.device)
        # 转换为[-1,1]坐标系的位移
        normalized_displacement = pixel_displacement / torch.tensor([W-1, H-1], device=self.device) * 2

        # 应用位移
        sampling_grid = base_grid + normalized_displacement

        # 扩展到batch维度
        sampling_grid = sampling_grid.unsqueeze(0).repeat(B, 1, 1, 1)  # [B, H, W, 2]

        # 使用grid_sample进行双线性插值
        transformed_x = F.grid_sample(x, sampling_grid, mode='bilinear',
                                    padding_mode='border', align_corners=True)

        return transformed_x

    def _apply_appearance_transform(self, x, appearance_field):
        """应用外观变换
        Args:
            x: [B, 3, H, W] 输入图像
            appearance_field: [H, W, 3] 外观调整场
        Returns:
            transformed_x: [B, 3, H, W] 外观变换后图像
        """
        B, C, H, W = x.shape

        # 将外观场扩展到batch维度并调整维度顺序
        appearance_adjustment = appearance_field.permute(2, 0, 1).unsqueeze(0)  # [1, 3, H, W]
        appearance_adjustment = appearance_adjustment.repeat(B, 1, 1, 1)  # [B, 3, H, W]

        # 应用外观调整
        transformed_x = x + appearance_adjustment

        # 确保像素值在合理范围内
        transformed_x = torch.clamp(transformed_x, 0.0, 1.0)

        return transformed_x

    def _generate_image_summary(self, x):
        """生成图像摘要：8x8分块的均值和标准差
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            summary: [B, 384] 图像摘要 (8*8*6=384维)
        """
        B, C, H, W = x.shape

        # 计算分块大小
        block_h = H // 8
        block_w = W // 8

        summaries = []

        for b in range(B):
            block_stats = []

            for i in range(8):
                for j in range(8):
                    # 提取分块
                    start_h, end_h = i * block_h, (i + 1) * block_h
                    start_w, end_w = j * block_w, (j + 1) * block_w
                    block = x[b, :, start_h:end_h, start_w:end_w]  # [3, block_h, block_w]

                    # 计算均值和标准差
                    block_mean = block.mean(dim=(1, 2))  # [3]
                    block_std = block.std(dim=(1, 2))   # [3]

                    # 合并统计量
                    block_stat = torch.cat([block_mean, block_std])  # [6]
                    block_stats.append(block_stat)

            # 合并所有分块统计量
            image_summary = torch.cat(block_stats)  # [8*8*6=384]
            summaries.append(image_summary)

        return torch.stack(summaries)  # [B, 384]
