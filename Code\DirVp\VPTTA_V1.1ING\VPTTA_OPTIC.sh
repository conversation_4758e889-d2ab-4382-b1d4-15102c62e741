#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/

#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REF<PERSON>GE_Valid, Drishti_GS]
Source=Drishti_GS

#Optimizer
optimizer=Adam
lr=0.005

#Hyperparameters
memory_size=40
neighbor=16
warm_n=5

#DGAF-Prompt参数
control_grid_size=16
input_alignment_lambda=0.5
summary_blocks=8
volume_lambda=0.01
smooth_lambda=0.005

#Command
cd OPTIC
CUDA_VISIBLE_DEVICES=0 python vptta.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--optimizer $optimizer --lr $lr \
--memory_size $memory_size --neighbor $neighbor --warm_n $warm_n \
--control_grid_size $control_grid_size --input_alignment_lambda $input_alignment_lambda \
--summary_blocks $summary_blocks --volume_lambda $volume_lambda --smooth_lambda $smooth_lambda