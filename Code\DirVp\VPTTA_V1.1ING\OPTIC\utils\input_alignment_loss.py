import torch
import torch.nn as nn
import torch.nn.functional as F


def ssim(x, y, window_size=11, size_average=True, c1=0.01**2, c2=0.03**2):
    """计算结构相似性指数 (SSIM)
    Args:
        x: [B, C, H, W] 第一个图像
        y: [B, C, H, W] 第二个图像
        window_size: 滑动窗口大小
        size_average: 是否对所有像素求平均
        c1, c2: 稳定性常数
    Returns:
        ssim_value: SSIM值
    """
    # 创建高斯窗口
    def create_window(window_size, channel):
        _1D_window = torch.exp(-(torch.arange(window_size) - window_size//2)**2 / (2.0 * (window_size/6)**2))
        _1D_window = _1D_window / _1D_window.sum()
        _2D_window = _1D_window.unsqueeze(1) @ _1D_window.unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window
    
    channel = x.size(1)
    window = create_window(window_size, channel).to(x.device).type_as(x)
    
    # 计算均值
    mu1 = F.conv2d(x, window, padding=window_size//2, groups=channel)
    mu2 = F.conv2d(y, window, padding=window_size//2, groups=channel)
    
    mu1_sq = mu1.pow(2)
    mu2_sq = mu2.pow(2)
    mu1_mu2 = mu1 * mu2
    
    # 计算方差和协方差
    sigma1_sq = F.conv2d(x * x, window, padding=window_size//2, groups=channel) - mu1_sq
    sigma2_sq = F.conv2d(y * y, window, padding=window_size//2, groups=channel) - mu2_sq
    sigma12 = F.conv2d(x * y, window, padding=window_size//2, groups=channel) - mu1_mu2
    
    # 计算SSIM
    numerator = (2 * mu1_mu2 + c1) * (2 * sigma12 + c2)
    denominator = (mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2)
    
    ssim_map = numerator / denominator
    
    if size_average:
        return ssim_map.mean()
    else:
        return ssim_map.mean(1).mean(1).mean(1)


class InputAlignmentLoss(nn.Module):
    """输入对齐损失 (L_in)
    
    通过强制施加约束，使得经过模型调整后的"原始图像"和经过同样调整后的"增强图像"
    在结构上保持一致，基于SSIM指标实现。
    """
    
    def __init__(self, window_size=11):
        super().__init__()
        self.window_size = window_size
        
    def forward(self, original_adjusted, augmented_adjusted):
        """计算输入对齐损失
        Args:
            original_adjusted: [B, C, H, W] 经过提示调整后的原始图像
            augmented_adjusted: [B, C, H, W] 经过提示调整后的增强图像
        Returns:
            loss: 输入对齐损失值 (1 - SSIM)
        """
        # 计算SSIM值
        ssim_value = ssim(original_adjusted, augmented_adjusted, 
                         window_size=self.window_size, size_average=True)
        
        # L_in = 1 - SSIM
        loss = 1.0 - ssim_value
        
        return loss
