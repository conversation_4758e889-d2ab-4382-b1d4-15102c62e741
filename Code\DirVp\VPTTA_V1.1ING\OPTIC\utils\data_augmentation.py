import torch
import torch.nn.functional as F
import numpy as np


def apply_random_augmentation(x):
    """对输入图像应用随机数据增强
    Args:
        x: [B, C, H, W] 输入图像张量
    Returns:
        augmented_x: [B, C, H, W] 增强后的图像张量
    """
    B, C, H, W = x.shape
    device = x.device
    
    # 创建输出张量
    augmented_x = x.clone()
    
    for b in range(B):
        # 1. 几何变换
        # 水平翻转 (50%概率)
        if torch.rand(1).item() < 0.5:
            augmented_x[b] = torch.flip(augmented_x[b], dims=[2])
        
        # 旋转 (±5度)
        angle = (torch.rand(1).item() - 0.5) * 10  # -5到5度
        if abs(angle) > 0.1:  # 避免过小的旋转
            augmented_x[b] = _rotate_image(augmented_x[b], angle)
        
        # 缩放 (0.95-1.05倍)
        scale = 0.95 + torch.rand(1).item() * 0.1  # 0.95到1.05
        if abs(scale - 1.0) > 0.01:  # 避免过小的缩放
            augmented_x[b] = _scale_image(augmented_x[b], scale)
        
        # 2. 外观变换
        # 亮度调整 (0.8-1.2倍)
        brightness_factor = 0.8 + torch.rand(1).item() * 0.4  # 0.8到1.2
        augmented_x[b] = augmented_x[b] * brightness_factor
        
        # 对比度调整 (0.9-1.1倍)
        contrast_factor = 0.9 + torch.rand(1).item() * 0.2  # 0.9到1.1
        mean_val = augmented_x[b].mean()
        augmented_x[b] = (augmented_x[b] - mean_val) * contrast_factor + mean_val
        
        # 3. 高斯噪声 (σ=0.01)
        noise = torch.randn_like(augmented_x[b]) * 0.01
        augmented_x[b] = augmented_x[b] + noise
        
        # 确保像素值在[0,1]范围内
        augmented_x[b] = torch.clamp(augmented_x[b], 0.0, 1.0)
    
    return augmented_x


def _rotate_image(img, angle_degrees):
    """旋转单张图像
    Args:
        img: [C, H, W] 图像张量
        angle_degrees: 旋转角度（度）
    Returns:
        rotated_img: [C, H, W] 旋转后的图像
    """
    C, H, W = img.shape
    
    # 转换角度为弧度
    angle_rad = np.deg2rad(angle_degrees)
    
    # 创建旋转矩阵
    cos_a = np.cos(angle_rad)
    sin_a = np.sin(angle_rad)
    
    # 创建仿射变换矩阵
    theta = torch.tensor([[cos_a, -sin_a, 0],
                         [sin_a, cos_a, 0]], dtype=torch.float32, device=img.device)
    theta = theta.unsqueeze(0)  # [1, 2, 3]
    
    # 生成网格
    grid = F.affine_grid(theta, [1, C, H, W], align_corners=False)
    
    # 应用变换
    img_batch = img.unsqueeze(0)  # [1, C, H, W]
    rotated = F.grid_sample(img_batch, grid, mode='bilinear', 
                           padding_mode='border', align_corners=False)
    
    return rotated.squeeze(0)  # [C, H, W]


def _scale_image(img, scale_factor):
    """缩放单张图像
    Args:
        img: [C, H, W] 图像张量
        scale_factor: 缩放因子
    Returns:
        scaled_img: [C, H, W] 缩放后的图像
    """
    C, H, W = img.shape
    
    # 创建缩放矩阵
    theta = torch.tensor([[scale_factor, 0, 0],
                         [0, scale_factor, 0]], dtype=torch.float32, device=img.device)
    theta = theta.unsqueeze(0)  # [1, 2, 3]
    
    # 生成网格
    grid = F.affine_grid(theta, [1, C, H, W], align_corners=False)
    
    # 应用变换
    img_batch = img.unsqueeze(0)  # [1, C, H, W]
    scaled = F.grid_sample(img_batch, grid, mode='bilinear', 
                          padding_mode='border', align_corners=False)
    
    return scaled.squeeze(0)  # [C, H, W]
